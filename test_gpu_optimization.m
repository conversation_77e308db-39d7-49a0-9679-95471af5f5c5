% GPU优化测试脚本
clc; clear; close all;

fprintf('=== GPU优化测试 ===\n');

%% GPU初始化测试
try
    if gpuDeviceCount > 0
        gpu_device = gpuDevice();
        fprintf('GPU设备: %s (计算能力: %.1f)\n', gpu_device.Name, gpu_device.ComputeCapability);
        fprintf('GPU内存: %.1f GB\n', gpu_device.AvailableMemory/1024^3);
        use_gpu = true;
    else
        fprintf('未检测到GPU设备，使用CPU计算\n');
        use_gpu = false;
    end
catch
    fprintf('GPU初始化失败，使用CPU计算\n');
    use_gpu = false;
end

%% 测试数据创建
fprintf('\n=== 创建测试数据 ===\n');
test_size = [500, 1000];  % 模拟Backscatter数据大小
fprintf('测试数据大小: %d x %d\n', test_size(1), test_size(2));

% 创建模拟数据
test_data = rand(test_size) * 0.1;  % 模拟Backscatter数据
height_data = linspace(0, 15, test_size(1))';  % 模拟高度数据

%% GPU数据传输测试
if use_gpu
    fprintf('\n=== GPU数据传输测试 ===\n');
    try
        tic;
        gpu_data = gpuArray(test_data);
        gpu_height = gpuArray(height_data);
        transfer_time = toc;
        fprintf('数据传输到GPU耗时: %.3f 秒\n', transfer_time);
        
        % 测试GPU计算
        tic;
        gpu_result = gpu_data .* 2 + sin(gpu_height);
        gpu_compute_time = toc;
        fprintf('GPU计算耗时: %.3f 秒\n', gpu_compute_time);
        
        % 数据传回CPU
        tic;
        cpu_result = gather(gpu_result);
        gather_time = toc;
        fprintf('数据传回CPU耗时: %.3f 秒\n', gather_time);
        
        fprintf('GPU总耗时: %.3f 秒\n', transfer_time + gpu_compute_time + gather_time);
        
    catch ME
        fprintf('GPU计算失败: %s\n', ME.message);
        use_gpu = false;
    end
end

%% CPU计算对比
fprintf('\n=== CPU计算对比 ===\n');
tic;
cpu_result_direct = test_data .* 2 + sin(height_data);
cpu_time = toc;
fprintf('CPU计算耗时: %.3f 秒\n', cpu_time);

%% 结果验证
if use_gpu && exist('cpu_result', 'var')
    max_diff = max(abs(cpu_result(:) - cpu_result_direct(:)));
    fprintf('\n=== 结果验证 ===\n');
    fprintf('GPU和CPU结果最大差异: %.2e\n', max_diff);
    if max_diff < 1e-10
        fprintf('✓ GPU和CPU计算结果一致\n');
    else
        fprintf('✗ GPU和CPU计算结果存在差异\n');
    end
end

%% 性能总结
fprintf('\n=== 性能总结 ===\n');
if use_gpu && exist('gpu_compute_time', 'var')
    total_gpu_time = transfer_time + gpu_compute_time + gather_time;
    speedup = cpu_time / total_gpu_time;
    fprintf('CPU时间: %.3f 秒\n', cpu_time);
    fprintf('GPU总时间: %.3f 秒\n', total_gpu_time);
    fprintf('加速比: %.2fx\n', speedup);
    if speedup > 1
        fprintf('✓ GPU加速有效\n');
    else
        fprintf('✗ GPU加速无效（数据量可能太小）\n');
    end
else
    fprintf('仅使用CPU计算: %.3f 秒\n', cpu_time);
end

fprintf('\n=== 测试完成 ===\n');
